:root {
  --color-background: #0f172a; /* dark */
  --color-background-alt: #080d1b; /* darker */
  --color-text-primary: #f6f6f7; /* light */
  --color-text-secondary: rgba(246, 246, 247, 0.7);
  --color-accent: #3b82f6; /* primary */
  --color-border: #374151; /* gray-700 */
}
html.light {
  --color-background: #f3f4f6; /* gray-100 */
  --color-background-alt: #ffffff; /* white */
  --color-text-primary: #1f2937; /* gray-800 */
  --color-text-secondary: #4b5563; /* gray-600 */
  --color-accent: #2563eb; /* accent */
  --color-border: #e5e7eb; /* gray-200 */
}
body {
  background-color: var(--color-background);
  color: var(--color-text-primary);
  transition: background-color 0.3s, color 0.3s;
}
.bg-background {
  background-color: var(--color-background);
}
.bg-background-alt {
  background-color: var(--color-background-alt);
}
.text-text-primary {
  color: var(--color-text-primary);
}
.text-text-secondary {
  color: var(--color-text-secondary);
}
.text-accent {
  color: var(--color-accent);
}
.border-default {
  border-color: var(--color-border);
}
body {
  background-color: #0f172a;
  color: #f6f6f7;
  font-family: "Inter", sans-serif;
}
body,
a,
button {
  cursor: none;
}
#cursor-dot {
  position: fixed;
  top: 0;
  left: 0;
  width: 8px;
  height: 8px;
  background-color: #3b82f6;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 9999;
  transition: width 0.3s, height 0.3s;
}
#cursor-outline {
  position: fixed;
  top: 0;
  left: 0;
  width: 40px;
  height: 40px;
  border: 2px solid #3b82f6;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 9999;
  transition: width 0.3s, height 0.3s, border-width 0.3s, opacity 0.3s,
    background-color 0.3s;
}
#cursor-outline.hover {
  width: 60px;
  height: 60px;
  background-color: rgba(59, 130, 246, 0.2);
  border-width: 3px;
}
.gradient-text {
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% auto;
  transition: background-position 0.5s ease;
}
.gradient-text:hover {
  background-position: -100% center;
}
.gradient-border {
  position: relative;
}
.gradient-border::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 100px;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  border-radius: 2px;
}
.card-hover {
  transition: all 0.3s ease;
}
.card-hover:hover {
  transform: translateY(-8px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  border-color: #3b82f6;
}
.btn {
  transition: all 0.3s ease;
}
.btn:active {
  transform: scale(0.97);
}
.btn-primary {
  background: linear-gradient(90deg, #3b82f6, #2563eb);
}
.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(59, 130, 246, 0.4);
}
.btn-outline:hover {
  background-color: rgba(59, 130, 246, 0.1);
  transform: translateY(-2px);
}
header.scrolled {
  background-color: rgba(8, 13, 27, 0.8);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
}
.spotlight-card {
  position: relative;
  overflow: hidden;
  background-color: #0f172a; /* dark color */
  border: 1px solid #1e293b; /* gray-800 */
}
.spotlight-card::before {
  content: "";
  position: absolute;
  left: var(--mouse-x, 50%);
  top: var(--mouse-y, 50%);
  width: 250px;
  height: 250px;
  background: radial-gradient(
    circle,
    rgba(59, 130, 246, 0.15) 0%,
    rgba(59, 130, 246, 0) 70%
  );
  transform: translate(-50%, -50%);
  transition: opacity 0.3s;
  opacity: 0;
  pointer-events: none;
}
.spotlight-card:hover::before {
  opacity: 1;
}
#spline-container {
  z-index: 0;
  opacity: 0.15;
  filter: blur(5px);
}
#spline-container spline-viewer {
  width: 100%;
  height: 100%;
  position: absolute;
}
