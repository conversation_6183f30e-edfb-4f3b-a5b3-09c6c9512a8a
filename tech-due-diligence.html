<!doctype html>
<html lang="en" class="scroll-smooth">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Tech Due Diligence for Investors & Acquirers | AlphaPebble</title>
    <meta
      name="description"
      content="AlphaPebble offers productized technical due diligence to help VCs, acquirers, and founders audit codebases before committing to a deal."
    />
    <link rel="preconnect" href="https://cdn.tailwindcss.com" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              dark: "#0F172A",
              darker: "#080D1B",
              primary: "#3B82F6",
              light: "#F6F6F7",
            },
            fontFamily: { sans: ["Inter", "sans-serif"] },
          },
        },
      };
    </script>
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap"
      rel="stylesheet"
    />
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet" />
    <style>
      body {
        background-color: #0f172a;
        color: #f6f6f7;
        font-family: "Inter", sans-serif;
      }
      .prose-custom p,
      .prose-custom li {
        color: #f6f6f7b3;
      }
      .btn {
        transition: all 0.3s ease;
      }
      .btn:active {
        transform: scale(0.97);
      }
      .btn-primary {
        background: linear-gradient(90deg, #3b82f6, #2563eb);
      }
      .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(59, 130, 246, 0.4);
      }
      header.scrolled {
        background-color: rgba(8, 13, 27, 0.8);
        backdrop-filter: blur(10px);
      }
    </style>
  </head>
  <body class="bg-dark text-light">
    <header
      class="py-4 fixed top-0 left-0 right-0 z-50 transition-all duration-300"
    >
      <div class="container mx-auto px-6 md:px-12">
        <nav class="flex justify-between items-center">
          <a href="../index.html" class="flex items-center gap-3">
            <img
              src="../images/logo.svg"
              alt="AlphaPebble Logo"
              class="h-12 w-12 rounded-full bg-white p-0.5"
            />
            <span class="text-2xl font-bold text-light">AlphaPebble</span>
          </a>
          <div class="hidden md:flex items-center gap-8">
            <a
              href="../index.html#services"
              class="text-light/80 hover:text-light transition-colors"
              >What We Do</a
            >
            <a
              href="index.html"
              class="text-light hover:text-light transition-colors font-semibold"
              >Insights</a
            >
            <a
              href="../index.html#contact"
              class="btn btn-primary px-5 py-2.5 rounded-lg text-white font-medium"
              >Let's Talk</a
            >
          </div>
          <button
            id="mobile-menu-button"
            class="md:hidden text-light p-2 rounded-md"
            aria-controls="mobile-menu"
            aria-expanded="false"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 6h16M4 12h16M4 18h16"
              />
            </svg>
          </button>
        </nav>
      </div>
    </header>

    <main class="pt-32 pb-20">
      <article class="container mx-auto px-6 md:px-12">
        <header class="max-w-3xl mx-auto text-center mb-16" data-aos="fade-up">
          <a
            href="index.html"
            class="inline-block bg-primary/10 text-primary text-sm font-semibold px-4 py-1 rounded-full mb-6"
            >Due Diligence</a
          >
          <h1 class="text-4xl md:text-5xl font-extrabold mb-4">
            Tech Due Diligence for Investors & Acquirers
          </h1>
          <p class="text-lg text-light/60">5 min read • May 5, 2025</p>
        </header>

        <div class="prose-custom max-w-3xl mx-auto text-lg">
          <p class="lead text-xl text-light/80" data-aos="fade-up">
            We've productized technical due diligence to help VCs, acquirers,
            and founders audit codebases and infrastructure before committing to
            a deal. Get a fast technical gut check before the term sheet.
          </p>
          <div
            class="text-center mt-16 p-8 bg-darker border border-primary/30 rounded-2xl"
            data-aos="zoom-in"
          >
            <h2 class="text-3xl font-bold mb-4">
              Make Informed Decisions with Confidence
            </h2>
            <p class="text-light/80 mb-6">
              Our due diligence service helps you move forward on deals, armed
              with the technical insights you need.
            </p>
            <a href="../index.html#contact" class="btn btn-primary px-6 py-3"
              >Request a Diligence Report</a
            >
          </div>
        </div>
      </article>
    </main>

    <footer class="py-12 border-t border-gray-800">
      <div class="container mx-auto px-6 md:px-12 text-center">
        <div class="flex justify-center gap-6 mb-8">
          <a
            href="../index.html#services"
            class="text-light/60 hover:text-light"
            >Services</a
          >
          <a href="index.html" class="text-light/60 hover:text-light"
            >All Insights</a
          >
          <a href="../index.html#contact" class="text-light/60 hover:text-light"
            >Contact</a
          >
        </div>
        <p class="text-light/60">
          &copy; <span id="copyright-year"></span> AlphaPebble. All rights
          reserved.
        </p>
      </div>
    </footer>

    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
      AOS.init({ duration: 700, once: true, offset: 50 });
      document.addEventListener("DOMContentLoaded", function () {
        const header = document.querySelector("header");
        header &&
          window.addEventListener("scroll", function () {
            window.scrollY > 50
              ? header.classList.add("scrolled")
              : header.classList.remove("scrolled");
          });
        const yearSpan = document.getElementById("copyright-year");
        yearSpan && (yearSpan.textContent = new Date().getFullYear());
      });
    </script>
  </body>
</html>
